import type { UserIdentity } from "convex/server";

/**
 * テスト用の認証ヘルパー関数
 */

/**
 * テスト用のissuerを生成する
 * convex-testのwithIdentityが自動的に生成するissuerフォーマットを模倣
 * convex-testは "https://convex.test" をissuerとして使用
 */
export const TEST_ISSUER = "https://convex.test";

/**
 * subjectからtokenIdentifierフォーマットを生成する
 * 本番環境のClerkが生成する形式に合わせる
 * @param subject - ユーザーの一意識別子（例: "user_test123456789"）
 * @returns tokenIdentifier形式の文字列（例: "https://auto-generated-issuer|user_test123456789"）
 */
export function createTokenIdentifier(subject: string): string {
	return `${TEST_ISSUER}|${subject}`;
}

/**
 * tokenIdentifierからsubjectを抽出する
 * @param tokenIdentifier - tokenIdentifier形式の文字列
 * @returns subject部分の文字列
 */
export function extractSubject(tokenIdentifier: string): string {
	const parts = tokenIdentifier.split("|");
	return parts[parts.length - 1];
}

/**
 * テスト用のUserIdentityオブジェクトを作成する
 * @param subject - ユーザーの一意識別子
 * @returns UserIdentityオブジェクト
 */
export function createTestUserIdentity(subject: string): UserIdentity {
	const tokenIdentifier = createTokenIdentifier(subject);
	return {
		issuer: TEST_ISSUER,
		subject,
		tokenIdentifier,
		name: `Test User ${subject}`,
		email: `${subject}@test.example.com`,
		emailVerified: true,
		phoneNumber: undefined,
		phoneNumberVerified: false,
		pictureUrl: undefined,
		updatedAt: new Date().toISOString(),
	};
}

/**
 * convex-testのwithIdentityで使用するための認証設定を生成する
 * @param subject - ユーザーの一意識別子
 * @returns withIdentityに渡す認証情報
 */
export function createAuthForTesting(subject: string): { subject: string } {
	// convex-testのwithIdentityはsubjectのみを受け取り、
	// 内部でissuerを自動生成してtokenIdentifierを作成する
	return { subject };
}

/**
 * テストデータベースに保存するためのuserIdを生成する
 * 本番環境と同じtokenIdentifier形式を使用する
 * @param subject - ユーザーの一意識別子
 * @returns データベースに保存するuserId（tokenIdentifier形式）
 */
export function createUserIdForDatabase(subject: string): string {
	return createTokenIdentifier(subject);
}
